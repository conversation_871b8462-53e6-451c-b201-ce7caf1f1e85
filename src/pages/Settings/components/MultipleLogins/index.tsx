import { Button, Skeleton } from 'antd';
import { useEffect, useState } from 'react';
import { toast } from 'sonner';
import { useTerminateAllSessionsMutation } from '@/api/userApi';
import { MOCK_LOGIN_SESSIONS } from '@/utils/constants';
import { formatRelativeTime } from '@/utils/date';
import styles from './MultipleLogins.module.scss';
import clsx from 'clsx';

const MultipleLogins = () => {
  // TODO: Temporarily using mock data instead of API
  const sessionsData = { sessions: MOCK_LOGIN_SESSIONS };
  const isLoadingSessions = false;
  const loadError = null;

  // const {
  //   data: sessionsData,
  //   isLoading: isLoadingSessions,
  //   error: loadError,
  // } = useGetLoginSessionsQuery();

  const [terminateAllSessions] = useTerminateAllSessionsMutation();

  const [isTerminatingAll, setIsTerminatingAll] = useState(false);

  useEffect(() => {
    if (loadError) {
      const errorMessage =
        (loadError as { data?: { message?: string }; message?: string })?.data?.message ||
        (loadError as { message?: string })?.message ||
        'Failed to load login sessions';
      toast.error(errorMessage);
    }
  }, [loadError]);

  const handleTerminateAllSessions = async () => {
    setIsTerminatingAll(true);

    try {
      const result = await terminateAllSessions().unwrap();
      toast.success(`${result.terminatedCount} sessions terminated successfully`);
    } catch (error: unknown) {
      const errorMessage =
        (error as { data?: { message?: string }; message?: string })?.data?.message ||
        (error as { message?: string })?.message ||
        'Failed to terminate all sessions';
      toast.error(errorMessage);
      console.error('Terminate all sessions error:', error);
    } finally {
      setIsTerminatingAll(false);
    }
  };

  const sessions = sessionsData?.sessions || [];
  const activeSessions = sessions.filter((session) => session.status === 'active');
  const inactiveSessions = sessions.filter((session) => session.status === 'not_active');

  if (true) {
    return (
      <div className={clsx(styles.multipleLogins, styles.skeleton)}>
        <div className={styles.innerWrapper}>
          <div className={styles.header}>
            <Skeleton active />
            <Skeleton active />
          </div>

          <div className={styles.sessionsListHeading}>
            <h3 className={styles.deviceHeading}>Device</h3>
            <h3 className={styles.ipAddressHeading}>IP Address</h3>
          </div>

          <div className={styles.sessionsList}>
            {Array.from({ length: 3 }).map((_, index) => (
              <div key={index} className={styles.sessionItem}>
                <div className={styles.sessionInfo}>
                  <Skeleton.Node active />
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>
    );
  }

  const isDisabled = !!loadError;

  return (
    <div className={styles.multipleLogins}>
      <div className={styles.innerWrapper}>
        <div className={styles.header}>
          <h2 className={styles.title}>Multiple Logins</h2>
          {sessions.length > 0 && (
            <Button
              type="primary"
              danger
              onClick={handleTerminateAllSessions}
              loading={isTerminatingAll}
              disabled={isDisabled || isTerminatingAll}
              className={styles.terminateAllButton}
            >
              Terminate all sessions
            </Button>
          )}
        </div>

        <div className={styles.sessionsListHeading}>
          <h3 className={styles.deviceHeading}>Device</h3>
          <h3 className={styles.ipAddressHeading}>IP Address</h3>
        </div>

        <div className={styles.sessionsList}>
          {activeSessions.map((session) => (
            <div key={session.id} className={clsx(styles.sessionItem, styles.active)}>
              <div className={styles.sessionInfo}>
                <div className={styles.deviceInfo}>
                  <h3 className={styles.deviceName}>{session.deviceInfo}</h3>
                  <p className={styles.deviceStatus}>
                    <span className={styles.statusActive}>Active</span>
                  </p>
                </div>
                <div className={styles.sessionDetails}>
                  <p className={styles.ipAddress}>{session.ipAddress}</p>
                </div>
              </div>
            </div>
          ))}

          {inactiveSessions.map((session) => (
            <div key={session.id} className={clsx(styles.sessionItem, styles.inactive)}>
              <div className={styles.sessionInfo}>
                <div className={styles.deviceInfo}>
                  <div className={styles.deviceDetails}>
                    <h3 className={styles.deviceName}>{session.deviceInfo}</h3>
                    <p className={styles.deviceStatus}>
                      <span className={styles.statusInactive}>Not Active</span>
                      <span className={styles.lastActive}>
                        • Last active: {formatRelativeTime(session.lastActive)}
                      </span>
                    </p>
                  </div>
                </div>
                <div className={styles.sessionDetails}>
                  <p className={styles.ipAddress}>{session.ipAddress}</p>
                </div>
              </div>
            </div>
          ))}

          {sessions.length === 0 && !isLoadingSessions && (
            <div className={styles.emptyState}>
              <p>No active sessions found</p>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export { MultipleLogins };
